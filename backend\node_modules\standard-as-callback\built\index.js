"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const utils_1 = require("./utils");
function throwLater(e) {
    setTimeout(function () {
        throw e;
    }, 0);
}
function asCallback(promise, nodeback, options) {
    if (typeof nodeback === "function") {
        promise.then((val) => {
            let ret;
            if (options !== undefined &&
                Object(options).spread &&
                Array.isArray(val)) {
                ret = utils_1.tryCatch(nodeback).apply(undefined, [null].concat(val));
            }
            else {
                ret =
                    val === undefined
                        ? utils_1.tryCatch(nodeback)(null)
                        : utils_1.tryCatch(nodeback)(null, val);
            }
            if (ret === utils_1.errorObj) {
                throwLater(ret.e);
            }
        }, (cause) => {
            if (!cause) {
                const newReason = new Error(cause + "");
                Object.assign(newReason, { cause });
                cause = newReason;
            }
            const ret = utils_1.tryCatch(nodeback)(cause);
            if (ret === utils_1.errorObj) {
                throwLater(ret.e);
            }
        });
    }
    return promise;
}
exports.default = asCallback;
