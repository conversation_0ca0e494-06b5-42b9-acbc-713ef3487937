{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.21.0", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.8.4", "morgan": "^1.10.0", "socket.io": "^4.8.1"}, "devDependencies": {}, "description": ""}